import {Button} from '@/components/ui/button';

export function RSVPButton() {
  return (
    <div>
      <p className="text-sm md:text-base italic font-arial  max-w-[600px] mx-auto">
        📥 If you received a Save the Date email or text, please fill out this address
        collection if you have not done so already 🙏
      </p>

      <a href="https://form.jotform.com/251896494421062" target="_blank" rel="noopener noreferrer">
        <Button
          size="lg"
          className="text-2xl md:text-3xl font-black mt-4 py-6 rounded-lg
           bg-gradient-to-r from-steel-pink via-lavender-pink to-steel-pink text-white
           border-2 border-steel-pink
           animate-pulse-glow animate-float
           hover:scale-110 hover:border-lavender-pink hover:bg-gradient-to-r hover:from-lavender-pink hover:via-steel-pink hover:to-lavender-pink
           transition-all duration-300 ease-out
           relative overflow-hidden
           before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent
           before:translate-x-[-100%] hover:before:translate-x-[100%] before:transition-transform before:duration-700"
        >
          🏠 Address Collection Form
        </Button>
      </a>
    </div>
  );
}
