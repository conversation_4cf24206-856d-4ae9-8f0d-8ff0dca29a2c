import {Button} from '@/components/ui/button';

export function RSVPButton() {
  return (
    <div>
      <p className="text-sm md:text-base italic font-arial  max-w-[600px] mx-auto">
        📥 If you received a Save the Date email or text, please fill out this address
        collection if you have not done so already 🙏
      </p>

      <a href="https://form.jotform.com/251896494421062" target="_blank" rel="noopener noreferrer">
        <Button
          size="lg"
          className="text-2xl md:text-3xl font-black mt-4 py-6 rounded-lg border-2 border-white
           bg-gradient-to-r from-steel-pink to-syracuse-orange text-white shadow-xl shadow-steel-pink/40
           hover:scale-110 hover:shadow-2xl hover:shadow-steel-pink/60 hover:from-lavender-pink hover:to-syracuse-orange
           transition-all duration-300 animate-pulse"
        >
          Address Collection Form
        </Button>
      </a>
    </div>
  );
}
